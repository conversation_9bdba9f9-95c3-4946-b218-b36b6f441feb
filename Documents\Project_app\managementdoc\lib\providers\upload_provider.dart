import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_selector/file_selector.dart';
import '../models/upload_file_model.dart';
import '../models/document_model.dart';
import '../services/file_upload_service.dart';

import 'document_provider.dart';

class UploadProvider with ChangeNotifier {
  final FileUploadService _uploadService = FileUploadService();

  final List<UploadFileModel> _uploadQueue = [];
  bool _isUploading = false;

  // Context for accessing other providers
  BuildContext? _context;

  // Current category for uploads (set when uploading to specific category)
  String? _currentCategoryId;

  List<UploadFileModel> get uploadQueue => List.unmodifiable(_uploadQueue);
  bool get isUploading => _isUploading;

  // Statistics
  int get totalFiles => _uploadQueue.length;
  double get averageProgress {
    if (_uploadQueue.isEmpty) return 0.0;
    final totalProgress = _uploadQueue.fold<double>(
      0.0,
      (sum, file) => sum + file.progress,
    );
    return totalProgress / _uploadQueue.length;
  }

  int get completedFiles => _uploadQueue
      .where((file) => file.status == UploadStatus.completed)
      .length;

  int get failedFiles =>
      _uploadQueue.where((file) => file.status == UploadStatus.failed).length;

  // Security and validation statistics
  int get securityRejectedFiles => _uploadQueue
      .where(
        (file) =>
            file.status == UploadStatus.failed &&
            (file.errorMessage?.contains('security') == true ||
                file.errorMessage?.contains('dangerous') == true ||
                file.errorMessage?.contains('suspicious') == true),
      )
      .length;

  int get validationFailedFiles => _uploadQueue
      .where(
        (file) =>
            file.status == UploadStatus.failed &&
            file.errorMessage?.contains('validation') == true,
      )
      .length;

  // Add files to upload queue with comprehensive validation
  Future<void> addFiles(List<XFile> files, {String? categoryId}) async {
    // Set current category for this upload session
    _currentCategoryId = categoryId;

    for (final file in files) {
      try {
        // Simple file validation
        final isValid = await _validateFile(file);

        final uploadFile = UploadFileModel.fromXFile(
          file,
          categoryId: categoryId,
        );

        if (!isValid) {
          // File failed validation - add as failed
          final failedFile = uploadFile.copyWith(
            status: UploadStatus.failed,
            errorMessage:
                'File validation failed - unsupported file type or too large',
          );
          _uploadQueue.add(failedFile);
        } else {
          // File passed validation - get file size
          final bytes = await file.readAsBytes();
          final updatedFile = uploadFile.copyWith(fileSize: bytes.length);
          _uploadQueue.add(updatedFile);
        }
      } catch (e) {
        // Add as failed file if validation throws exception
        final uploadFile = UploadFileModel.fromXFile(
          file,
          categoryId: categoryId,
        );
        final failedFile = uploadFile.copyWith(
          status: UploadStatus.failed,
          errorMessage: 'Validation error: ${e.toString()}',
        );
        _uploadQueue.add(failedFile);
      }
    }

    notifyListeners();

    // Start upload process if not already running
    if (!_isUploading) {
      _startUploadProcess();
    }
  }

  // Add single file
  Future<void> addFile(XFile file, {String? categoryId}) async {
    await addFiles([file], categoryId: categoryId);
  }

  // Start upload process
  Future<void> _startUploadProcess() async {
    if (_isUploading) return;

    _isUploading = true;
    notifyListeners();

    final pendingFiles = _uploadQueue
        .where((file) => file.status == UploadStatus.pending)
        .toList();

    // Upload files concurrently (max 3 at a time)
    const maxConcurrent = 3;
    final futures = <Future>[];

    for (int i = 0; i < pendingFiles.length; i += maxConcurrent) {
      final batch = pendingFiles.skip(i).take(maxConcurrent);
      for (final file in batch) {
        futures.add(_uploadFile(file));
      }

      // Wait for current batch to complete before starting next
      if (futures.length >= maxConcurrent) {
        await Future.wait(futures);
        futures.clear();
      }
    }

    // Wait for remaining uploads
    if (futures.isNotEmpty) {
      await Future.wait(futures);
    }

    _isUploading = false;
    notifyListeners();
  }

  // Upload individual file with retry mechanism
  Future<void> _uploadFile(UploadFileModel file) async {
    const maxRetries = 3;
    int retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        // Update status to uploading
        _updateFileStatus(file.id, UploadStatus.uploading);
        file.uploadStartTime = DateTime.now();

        // Simulate AI processing for some files
        if (Random().nextBool()) {
          _updateFileAiProcessing(file.id, true);
        }

        // Upload with progress tracking (real Firebase upload)
        await _uploadService.uploadFile(
          file,
          onProgress: (progress) {
            _updateFileProgress(file.id, progress);
          },
        );

        // Mark as completed
        _updateFileStatus(file.id, UploadStatus.completed);
        file.uploadEndTime = DateTime.now();
        _updateFileAiProcessing(file.id, false);

        // Add to DocumentProvider
        _addToDocumentProvider(file, categoryId: _currentCategoryId);

        // Success - break out of retry loop
        break;
      } catch (e) {
        retryCount++;
        _updateFileAiProcessing(file.id, false);

        // Check if this is the last retry
        if (retryCount >= maxRetries) {
          _updateFileStatus(file.id, UploadStatus.failed);

          // Provide user-friendly error messages
          String errorMessage = e.toString();
          if (errorMessage.contains('File size exceeds')) {
            errorMessage = 'File too large (max 10MB)';
          } else if (errorMessage.contains('cancelled') ||
              errorMessage.contains('canceled')) {
            errorMessage = 'Upload cancelled by user';
          } else if (errorMessage.contains('network') ||
              errorMessage.contains('Network')) {
            errorMessage = 'Network error - check your connection';
          } else if (errorMessage.contains('permission') ||
              errorMessage.contains('Permission')) {
            errorMessage = 'Permission denied - check access rights';
          } else if (errorMessage.contains('StorageException') ||
              errorMessage.contains('storage')) {
            errorMessage = 'Storage error - please try again';
          } else if (errorMessage.contains('timeout') ||
              errorMessage.contains('Timeout')) {
            errorMessage = 'Upload timeout - try with smaller file';
          } else if (errorMessage.contains('quota') ||
              errorMessage.contains('Quota')) {
            errorMessage = 'Storage quota exceeded';
          } else if (errorMessage.contains('validation') ||
              errorMessage.contains('security')) {
            errorMessage =
                'File validation failed - check file type and content';
          } else {
            errorMessage = 'Upload failed - please try again';
          }

          _updateFileError(file.id, errorMessage);
          break; // Exit retry loop
        } else {
          // Wait before retrying
          await Future.delayed(Duration(seconds: retryCount * 2));
        }
      }
    }
  }

  // Pause upload
  void pauseUpload(String fileId) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      final file = _uploadQueue[fileIndex];
      if (file.status == UploadStatus.uploading) {
        _updateFileStatus(fileId, UploadStatus.paused);
        _uploadService.pauseUpload(fileId);
      } else if (file.status == UploadStatus.paused) {
        _updateFileStatus(fileId, UploadStatus.uploading);
        _uploadService.resumeUpload(fileId);
      }
    }
  }

  // Cancel upload
  void cancelUpload(String fileId) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      _uploadService.cancelUpload(fileId);
      _uploadQueue.removeAt(fileIndex);
      notifyListeners();
    }
  }

  // Retry failed upload
  Future<void> retryUpload(String fileId) async {
    final file = _uploadQueue.firstWhere((f) => f.id == fileId);
    if (file.status == UploadStatus.failed) {
      _updateFileStatus(fileId, UploadStatus.pending);
      _updateFileError(fileId, null);
      _updateFileProgress(fileId, 0.0);

      if (!_isUploading) {
        _startUploadProcess();
      }
    }
  }

  // Clear completed uploads
  void clearCompleted() {
    _uploadQueue.removeWhere((file) => file.status == UploadStatus.completed);
    notifyListeners();
  }

  // Clear all uploads
  void clearAll() {
    // Cancel all active uploads
    for (final file in _uploadQueue) {
      if (file.status == UploadStatus.uploading) {
        _uploadService.cancelUpload(file.id);
      }
    }

    _uploadQueue.clear();
    _isUploading = false;
    notifyListeners();
  }

  // Reset upload state (for reusing upload screen)
  void resetUploadState() {
    clearAll();
    _currentCategoryId = null;
    notifyListeners();
  }

  // Check if upload can be restarted
  bool get canRestartUpload {
    return !_isUploading &&
        _uploadQueue.every(
          (file) =>
              file.status == UploadStatus.completed ||
              file.status == UploadStatus.failed ||
              file.status == UploadStatus.cancelled,
        );
  }

  // Simple file validation
  Future<bool> _validateFile(XFile file) async {
    try {
      // Check file extension
      final extension = file.name.split('.').last.toLowerCase();
      final allowedExtensions = [
        'pdf',
        'doc',
        'docx',
        'pptx',
        'txt',
        'jpg',
        'jpeg',
        'png',
        'xlsx',
        'xls',
      ];

      if (!allowedExtensions.contains(extension)) {
        return false;
      }

      // Check file size (max 10MB)
      final bytes = await file.readAsBytes();
      if (bytes.length > 10 * 1024 * 1024) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  // Helper methods to update file properties
  void _updateFileStatus(String fileId, UploadStatus status) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      _uploadQueue[fileIndex].status = status;
      notifyListeners();
    }
  }

  void _updateFileProgress(String fileId, double progress) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      _uploadQueue[fileIndex].progress = progress;
      notifyListeners();
    }
  }

  void _updateFileError(String fileId, String? error) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      _uploadQueue[fileIndex].errorMessage = error;
      notifyListeners();
    }
  }

  void _updateFileAiProcessing(String fileId, bool isProcessing) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      _uploadQueue[fileIndex].isAiProcessing = isProcessing;
      notifyListeners();
    }
  }

  // Set context for accessing other providers
  void setContext(BuildContext context) {
    _context = context;
  }

  // Set current category for uploads
  void setCurrentCategory(String? categoryId) {
    _currentCategoryId = categoryId;
  }

  // Add uploaded file to DocumentProvider
  void _addToDocumentProvider(UploadFileModel file, {String? categoryId}) {
    if (_context == null) return;

    try {
      // Create document model
      final document = DocumentModel(
        id: file.id,
        fileName: file.fileName,
        fileSize: file.fileSize,
        fileType: file.fileType,
        filePath: file.file.path, // Use XFile path
        uploadedBy: 'current_user', // TODO: Get from auth
        uploadedAt: DateTime.now(),
        category: categoryId ?? 'uncategorized',
        status: 'active',
        permissions: ['current_user'],
        metadata: DocumentMetadata(
          description: 'Uploaded via mobile app',
          tags: _generateTags(file.fileName),
        ),
      );

      // Get DocumentProvider and add document
      final documentProvider = Provider.of<DocumentProvider>(
        _context!,
        listen: false,
      );

      if (categoryId != null) {
        // Add to specific category
        documentProvider.addDocumentToCategory(document, categoryId);
      } else {
        // Add to general documents
        documentProvider.addDocument(document);
      }
    } catch (e) {
      debugPrint('Error adding document to provider: $e');
    }
  }

  // Generate tags based on filename
  List<String> _generateTags(String fileName) {
    final tags = <String>[];
    final nameParts = fileName
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9\s]'), ' ')
        .split(' ')
        .where((part) => part.isNotEmpty && part.length > 2)
        .toList();

    tags.addAll(nameParts);

    // Add file type tag
    final extension = fileName.split('.').last.toLowerCase();
    tags.add(extension);

    return tags.take(10).toList(); // Limit to 10 tags
  }

  @override
  void dispose() {
    // Cancel all uploads when provider is disposed
    for (final file in _uploadQueue) {
      if (file.status == UploadStatus.uploading) {
        _uploadService.cancelUpload(file.id);
      }
    }
    super.dispose();
  }
}
